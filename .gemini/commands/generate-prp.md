# Create React Component PRP

## Component/Feature file: $ARGUMENTS

Generate a complete PRP for React component implementation with thorough research for Abaddon Pest Control Services website. Ensure context is passed to the AI agent to enable self-validation and iterative refinement. Read the feature file first to understand what React component/section needs to be created, how the examples provided help, and any other considerations.

The AI agent only gets the context you are appending to the PRP and training data. Assume the AI agent has access to the codebase and the same knowledge cutoff as you, so it's important that your research findings are included or referenced in the PRP. The Agent has Websearch capabilities, so pass URLs to React documentation, CSS examples, and pest control industry best practices.

## Research Process

1. **React Component Analysis**

   - Search for similar React components/patterns in the codebase
   - Identify existing components to reference in PRP
   - Note CSS naming conventions and mobile-first patterns
   - Check Jest/React Testing Library test patterns for validation approach
   - Review `src/styles/variables.css` for brand colors and spacing

2. **External Research**

   - React component best practices and patterns
   - CSS mobile-first responsive design examples
   - Pest control industry website examples and UX patterns
   - Philippine business website conventions
   - React documentation (include specific URLs)
   - Accessibility guidelines for service websites

3. **Brand & SEO Context** (critical for Abaddon)

   - Reference `SEO_RESEARCH.md` for company information
   - Use brand colors from `src/styles/variables.css`
   - Include Philippine market context (PHP currency, local pests, contact methods)
   - Integrate Abaddon logo and brand identity
   - Consider mobile-first approach for Philippine users

4. **User Clarification** (if needed)
   - Specific design patterns to mirror and where to find them?
   - Integration requirements and where to find them?

## PRP Generation

Using PRPs/templates/prp_base.md as template:

### Critical Context to Include and pass to the AI agent as part of the PRP

- **React Documentation**: URLs with specific sections for hooks, components, etc.
- **CSS Examples**: Real snippets from `src/styles/variables.css` and mobile-first patterns
- **Brand Guidelines**: Abaddon logo usage, color variables, typography
- **SEO Requirements**: Keywords, meta tags, Philippine market context
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Testing Patterns**: Jest/React Testing Library examples

### Implementation Blueprint

- Start with component structure and props interface (TypeScript)
- Reference existing CSS patterns and mobile-first approach
- Include responsive design strategy (mobile → tablet → desktop)
- Error handling and loading states for forms/interactions
- List tasks to be completed to fulfill the PRP in the order they should be completed
- Include SEO optimization steps (meta tags, structured data, etc.)

### Validation Gates (Must be Executable) for React/TypeScript

```bash
# TypeScript compilation and linting
npm run type-check && npm run lint

# Component tests
npm run test -- --coverage

# Build verification
npm run build

# Accessibility testing (if configured)
npm run test:a11y

# Mobile responsiveness check
# Manual verification on mobile devices/browser dev tools
```

**_ CRITICAL AFTER YOU ARE DONE RESEARCHING AND EXPLORING THE CODEBASE BEFORE YOU START WRITING THE PRP _**

**_ ULTRATHINK ABOUT THE PRP AND PLAN YOUR APPROACH THEN START WRITING THE PRP _**

## Output

Save as: `PRPs/{component-name}.md`

## Quality Checklist for React Components

- [ ] All necessary React/TypeScript context included
- [ ] Brand colors and CSS variables referenced
- [ ] Mobile-first responsive design approach documented
- [ ] Validation gates are executable by AI (npm scripts)
- [ ] References existing component patterns
- [ ] Clear implementation path with component structure
- [ ] Error handling and loading states documented
- [ ] SEO optimization requirements included
- [ ] Accessibility guidelines specified
- [ ] Philippine market context integrated
- [ ] Abaddon brand identity maintained

Score the PRP on a scale of 1-10 (confidence level to succeed in one-pass implementation using Claude)

Remember: The goal is one-pass implementation success through comprehensive context for Abaddon Pest Control Services React components.
