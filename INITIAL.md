## FEATURE:

- React single-page application for a pest control services company in the Philippines.
- Professional website showcasing pest control services with modern, mobile-first design.
- Contact forms, service listings, pricing information, and company information.
- Responsive design optimized for Philippine market and mobile users.

## SECTIONS TO INCLUDE:

The single-page application should include the following main sections:

- **Header** - Navigation menu with company logo and contact info
- **Hero Section** - Eye-catching banner with main value proposition
- **Services Section** - List of pest control services offered (termites, cockroaches, ants, mosquitoes, rats, etc.)
- **About Section** - Company information and expertise
- **Pricing Section** - Service packages and pricing in Philippine Peso (₱)
- **Contact Section** - Contact form, phone numbers, WhatsApp/Viber links
- **Footer** - Additional links, service areas, social media

## EXAMPLES:

If there are examples in the `examples/` folder, use them as reference for:

- Component structure and organization
- CSS styling patterns and mobile-first approach
- TypeScript interfaces and type definitions
- Testing patterns with Jest/React Testing Library

Don't copy any examples directly, but use them as inspiration for best practices in React development.

## DOCUMENTATION:

- React documentation: https://react.dev/
- TypeScript documentation: https://www.typescriptlang.org/docs/
- Jest Testing Library: https://testing-library.com/docs/react-testing-library/intro/

## SEO OPTIMIZATION (CRITICAL):

Based on research of Abaddon Pest Control Services Inc., implement these SEO elements:

### **Meta Tags & Schema:**

- **Title:** "Abaddon Pest Control Services - Licensed Termite & Pest Exterminator Cavite Philippines"
- **Description:** "FDA-licensed pest control services in Dasmariñas, Cavite. Specializing in termite control, cockroach, ant & rodent extermination. Free inspection. Call now!"
- **LocalBusiness Schema** with FDA license info (CCHUHSRR-RIVA-PCO-01-ER-732510)
- **Service Schema** for each pest type (termites, cockroaches, ants, mosquitoes, rats)

### **Target Keywords:**

- Primary: "pest control services Philippines", "termite control Cavite", "pest control Dasmariñas"
- Long-tail: "termite post construction treatment Cavite", "licensed pest control operator Cavite"
- Local: "pest control near me Cavite", "exterminator Paliparan Molino Road"

### **Content Strategy:**

- Service pages for each pest type with local context
- Location-based content (Dasmariñas, Muntinlupa, Parañaque coverage)
- Educational content about Philippine pest seasons and IPM methods
- FAQ section addressing tropical climate pest issues

## OTHER CONSIDERATIONS:

- Include a .env.example with placeholder values for contact information and API keys.
- Include comprehensive README with setup instructions and project structure.
- Use Vite or Create React App for project initialization.
- **CSS MUST be mobile-first** - base styles for mobile, then `@media (min-width: ...)` for larger screens.
- **Native CSS only** - create separate `.css` file for each component.
- **Use brand colors from logo** - Import `src/styles/variables.css` in all components.
- **Logo integration** - Use provided Abaddon logo (`src/assets/logo/abaddon-logo.png`).
- Include Philippine context: PHP currency, local pest types, WhatsApp/Viber contact options.
- Optimize images and ensure fast loading times for mobile users.
- **Business Info:** 42 Paliparan Molino Road Salawag, Dasmariñas City, Cavite
- **Specialization:** Integrated Pest Management (IPM) approach
- **Service Areas:** Dasmariñas, Muntinlupa, Parañaque, Metro Manila
