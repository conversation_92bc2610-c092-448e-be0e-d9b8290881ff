### 🔄 Project Awareness & Context

- **Always read `PLANNING.md`** at the start of a new conversation to understand the project's architecture, goals, style, and constraints.
- **Check `TASK.md`** before starting a new task. If the task isn’t listed, add it with a brief description and today's date.
- **Use consistent naming conventions, file structure, and architecture patterns** as described in `PLANNING.md`.
- **This is a React single-page application** for a pest control services company in the Philippines.

### 🧱 Code Structure & Modularity

- **Never create a component file longer than 300 lines of code.** If a component approaches this limit, refactor by splitting it into smaller components or custom hooks.
- **Organize code into clearly separated modules**, grouped by feature or responsibility.
  For React components this looks like:
  - `components/` - Reusable UI components
  - `sections/` - Page sections (Header, Hero, Services, About, Contact, Footer)
  - `hooks/` - Custom React hooks
  - `utils/` - Utility functions
  - `assets/` - Images, icons, and static files
  - `styles/` - Global CSS files and component-specific CSS files
- **Use clear, consistent imports** (prefer absolute imports with path aliases).
- **Use environment variables** for configuration (API keys, contact info, etc.).

### 🧪 Testing & Reliability

- **Always create Jest/React Testing Library tests for new components** and utility functions.
- **After updating any component logic**, check whether existing tests need to be updated. If so, do it.
- **Tests should live alongside components** or in a `__tests__` folder.
  - Include at least:
    - 1 test for component rendering
    - 1 test for user interactions
    - 1 test for edge cases/error states

### ✅ Task Completion

- **Mark completed tasks in `TASK.md`** immediately after finishing them.
- Add new sub-tasks or TODOs discovered during development to `TASK.md` under a “Discovered During Work” section.

### 📎 Style & Conventions

- **Use React with TypeScript** as the primary technology stack.
- **Follow React best practices**: functional components, hooks, proper state management.
- **Use native CSS only** - create a separate `.css` file for each component (e.g., `Header.css` for `Header.tsx`).
- **CSS MUST be mobile-first** - write base styles for mobile, then use `@media (min-width: ...)` for larger screens.
- **Use proper TypeScript types** for props, state, and API responses.
- **Follow consistent naming conventions**:
  - Components: PascalCase (e.g., `ServiceCard.tsx`)
  - Files/folders: kebab-case (e.g., `pest-control-services/`)
  - Variables/functions: camelCase
- **Write JSDoc comments for complex functions**:
  ```typescript
  /**
   * Calculates service pricing based on property size and pest type
   * @param propertySize - Size of property in square meters
   * @param pestType - Type of pest requiring treatment
   * @returns Estimated service cost in PHP
   */
  ```

### 🌏 Philippines Context & Localization

- **Use Philippine Peso (₱/PHP)** for all pricing displays.
- **Include common Philippine pest types**: termites, cockroaches, ants, mosquitoes, rats, etc.
- **Consider local business practices**: contact via WhatsApp, Viber, or SMS.
- **Use appropriate imagery** that reflects Philippine residential/commercial properties.
- **Include service areas** relevant to Philippine cities/regions.

### 📱 Responsive Design & UX

- **Mobile-first approach MANDATORY** - all CSS must start with mobile styles, then progressively enhance for tablets and desktop using `min-width` media queries.
- **Fast loading times** - optimize images and minimize bundle size.
- **Clear call-to-actions** - prominent contact buttons and forms.
- **Professional appearance** - build trust for pest control services.
- **Accessibility** - proper ARIA labels, keyboard navigation, color contrast.

### 📚 Documentation & Explainability

- **Update `README.md`** when new features are added, dependencies change, or setup steps are modified.
- **Comment complex business logic** and ensure everything is understandable to other developers.
- **Document component props** with TypeScript interfaces and JSDoc.

### 🧠 AI Behavior Rules

- **Never assume missing context. Ask questions if uncertain.**
- **Never hallucinate React libraries or APIs** – only use known, verified packages.
- **Always confirm component names and file paths** exist before referencing them.
- **Never delete or overwrite existing code** unless explicitly instructed to or if part of a task from `TASK.md`.
- **Consider Philippine business context** when making design or content decisions.
