## FEATURE:

- React single-page application for a pest control services company in the Philippines.
- Professional website showcasing pest control services with modern, mobile-first design.
- Contact forms, service listings, pricing information, and company information.
- Responsive design optimized for Philippine market and mobile users.

## SECTIONS TO INCLUDE:

The single-page application should include the following main sections:

- **Header** - Navigation menu with company logo and contact info
- **Hero Section** - Eye-catching banner with main value proposition
- **Services Section** - List of pest control services offered (termites, cockroaches, ants, mosquitoes, rats, etc.)
- **About Section** - Company information and expertise
- **Pricing Section** - Service packages and pricing in Philippine Peso (₱)
- **Contact Section** - Contact form, phone numbers, WhatsApp/Viber links
- **Footer** - Additional links, service areas, social media

## EXAMPLES:

If there are examples in the `examples/` folder, use them as reference for:

- Component structure and organization
- CSS styling patterns and mobile-first approach
- TypeScript interfaces and type definitions
- Testing patterns with Jest/React Testing Library

Don't copy any examples directly, but use them as inspiration for best practices in React development.

## DOCUMENTATION:

- React documentation: https://react.dev/
- TypeScript documentation: https://www.typescriptlang.org/docs/
- Jest Testing Library: https://testing-library.com/docs/react-testing-library/intro/

## OTHER CONSIDERATIONS:

- Include a .env.example with placeholder values for contact information and API keys.
- Include comprehensive README with setup instructions and project structure.
- Use Vite or Create React App for project initialization.
- **CSS MUST be mobile-first** - base styles for mobile, then `@media (min-width: ...)` for larger screens.
- **Native CSS only** - create separate `.css` file for each component.
- Include Philippine context: PHP currency, local pest types, WhatsApp/Viber contact options.
- Optimize images and ensure fast loading times for mobile users.
- Include proper meta tags for SEO and social media sharing.
