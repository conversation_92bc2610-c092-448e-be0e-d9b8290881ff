/* ===================================
   ABADDON PEST CONTROL - GLOBAL STYLES
   Mobile-first approach with brand colors
   =================================== */

/* Import CSS Variables */
@import './variables.css';

/* === RESET & BASE STYLES === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  
  /* Mobile-first: Base styles for mobile */
  font-size: var(--font-size-base);
}

/* === TYPOGRAPHY === */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

h1 {
  font-size: var(--font-size-3xl);
  color: var(--color-primary-gold);
}

h2 {
  font-size: var(--font-size-2xl);
  color: var(--color-text-primary);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

p {
  margin-bottom: var(--spacing-md);
  line-height: 1.7;
}

a {
  color: var(--color-primary-gold);
  text-decoration: none;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--color-primary-gold-dark);
  text-decoration: underline;
}

/* === BUTTONS === */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  
  /* Mobile-first: Smaller buttons on mobile */
  min-width: 120px;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
  box-shadow: var(--shadow-gold);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  box-shadow: var(--shadow-medium);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
  transform: translateY(-2px);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary-gold);
  border: 2px solid var(--color-primary-gold);
}

.btn-outline:hover {
  background-color: var(--color-primary-gold);
  color: var(--color-text-primary);
}

/* === CONTAINERS === */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  
  /* Mobile-first: Smaller padding on mobile */
  padding: 0 var(--spacing-md);
}

.section {
  padding: var(--spacing-2xl) 0;
}

.section-dark {
  background-color: var(--color-bg-dark);
  color: var(--color-text-light);
}

.section-gold {
  background: var(--gradient-gold);
  color: var(--color-text-primary);
}

/* === UTILITY CLASSES === */
.text-center {
  text-align: center;
}

.text-gold {
  color: var(--color-text-gold);
}

.text-muted {
  color: var(--color-text-muted);
}

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* === RESPONSIVE DESIGN - MOBILE FIRST === */

/* Tablet styles (768px and up) */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
  
  h1 {
    font-size: var(--font-size-4xl);
  }
  
  h2 {
    font-size: var(--font-size-3xl);
  }
  
  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    min-width: 150px;
  }
  
  .section {
    padding: var(--spacing-3xl) 0;
  }
}

/* Desktop styles (992px and up) */
@media (min-width: 992px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
  
  h1 {
    font-size: var(--font-size-5xl);
  }
  
  .btn {
    padding: var(--spacing-md) var(--spacing-xl);
    min-width: 180px;
  }
}

/* Large desktop styles (1200px and up) */
@media (min-width: 1200px) {
  .container {
    padding: 0 var(--spacing-2xl);
  }
}
